/**
 * Test script to verify PDF export functionality with resource handling
 */

import dotenv from 'dotenv';
dotenv.config();

const env = process.env.NEXT_NODE_ENV || 'development';

// Load configuration
import config from './shared/config/config.json';
import dbconfig from './shared/config/db.json';

global.config = JSON.parse(JSON.stringify(config))[env];
global.db = JSON.parse(JSON.stringify(dbconfig))[env];

// Import required functions
import { getRecipeByIdRaw } from './src/helper/recipe.helper';

/**
 * Test PDF export with a specific recipe
 */
async function testPDFExport() {
  console.log('🧪 Testing PDF Export with Resource Handling...');
  console.log('Environment:', env);
  console.log('Database:', global.db.database);
  
  try {
    // Test with a specific recipe ID (you can change this)
    const recipeId = 1; // Change this to a recipe ID that has resources
    const organizationId = 'test-org'; // Change this to your organization ID
    
    console.log(`📖 Fetching recipe ${recipeId}...`);
    
    // Get recipe data
    const recipe = await getRecipeByIdRaw(recipeId, organizationId);
    
    if (!recipe) {
      console.log('❌ Recipe not found');
      process.exit(1);
    }
    
    console.log('✅ Recipe found:', recipe.recipe_title);
    console.log('📋 Recipe resources:', recipe.resources?.length || 0);
    
    if (recipe.resources && recipe.resources.length > 0) {
      console.log('🔍 Resource details:');
      recipe.resources.forEach((resource: any, index: number) => {
        console.log(`  ${index + 1}. Type: ${resource.type}`);
        console.log(`     Link: ${resource.item_link || resource.item_detail?.item_link}`);
        console.log(`     Link Type: ${resource.item_link_type}`);
        console.log(`     MIME Type: ${resource.item_detail?.item_mime_type}`);
        console.log('     ---');
      });
    } else {
      console.log('⚠️  No resources found for this recipe');
    }
    
    // You can uncomment the following lines to actually test PDF generation
    // const { exportRecipeToPDF } = await import('./src/helper/recipe.helper');
    // console.log('📄 Generating PDF...');
    // const pdfBuffer = await exportRecipeToPDF(recipe);
    // console.log('✅ PDF generated successfully, size:', pdfBuffer.length, 'bytes');
    
    process.exit(0);
    
  } catch (error: any) {
    console.error('❌ Test failed with error:');
    console.error(error.message);
    console.error('Stack trace:');
    console.error(error.stack);
    
    process.exit(1);
  }
}

// Run the test
testPDFExport();
