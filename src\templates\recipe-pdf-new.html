<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{recipe_title}}</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
            line-height: 1.5;
            color: #1a1a1a;
            background: #ffffff;
            font-size: 10pt;
            margin: 0;
            padding: 0;
            font-weight: 400;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            text-rendering: optimizeLegibility;
        }

        .container {
            max-width: 100%;
            margin: 0;
            padding: 0;
            background: white;
        }

        .main-content {
            width: 100%;
            padding: 0 8mm;
            padding-bottom: 5mm;
        }

        .recipe-header {
            display: flex;
            align-items: flex-start;
            margin-bottom: 12pt;
            border-bottom: 2pt solid #007bff;
            padding-bottom: 8pt;
        }

        .recipe-title-section {
            flex: 1;
        }

        .recipe-title {
            font-size: 20pt;
            font-weight: 700;
            color: #1e293b;
            margin-bottom: 8pt;
            line-height: 1.2;
            letter-spacing: -0.025em;
            text-shadow: 0 1pt 2pt rgba(0, 0, 0, 0.05);
        }

        .recipe-subtitle {
            font-size: 12pt;
            color: #64748b;
            font-weight: 400;
            margin-bottom: 6pt;
            line-height: 1.4;
        }

        .recipe-description {
            font-size: 10pt;
            color: #475569;
            line-height: 1.5;
            max-width: 85%;
        }

        /* Enhanced recipe image container with better placeholder visibility */
        .recipe-image-container {
            width: 120pt;
            height: 80pt;
            flex-shrink: 0;
            margin-left: 16pt;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 6pt;
            position: relative;
        }

        /* Only show background for empty containers or containers with broken images */
        .recipe-image-container:empty,
        .recipe-image-container.no-image {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        }

        .recipe-image {
            max-width: 100%;
            max-height: 100%;
            object-fit: cover;
            border-radius: 4pt;
            display: block;
        }

        /* Enhanced placeholder styling when no image is present */
        .recipe-image-container:empty::before,
        .recipe-image-container.no-image::before {
            content: "RECIPE\AIMAGE";
            white-space: pre-line;
            text-align: center;
            font-size: 8pt;
            font-weight: 600;
            color: #64748b;
            line-height: 1.2;
            display: flex;
            align-items: center;
            justify-content: center;
            height: 100%;
        }

        /* Hide broken images and show placeholder */
        .recipe-image[src=""],
        .recipe-image:not([src]),
        .recipe-image[src*="undefined"],
        .recipe-image[src*="null"] {
            display: none;
        }

        /* Custom image placeholder for explicit no-image cases */
        .image-placeholder {
            width: 100%;
            height: 120pt;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #f8f9fa;
            border: 2pt dashed #dee2e6;
            border-radius: 8pt;
            color: #6c757d;
            font-size: 14pt;
            font-weight: 500;
        }

        .recipe-image-container:has(.recipe-image[src=""]),
        .recipe-image-container:has(.recipe-image:not([src])),
        .recipe-image-container:has(.recipe-image[src*="undefined"]),
        .recipe-image-container:has(.recipe-image[src*="null"]) {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        }

        .recipe-image-container:has(.recipe-image[src=""]):before,
        .recipe-image-container:has(.recipe-image:not([src])):before,
        .recipe-image-container:has(.recipe-image[src*="undefined"]):before,
        .recipe-image-container:has(.recipe-image[src*="null"]):before {
            content: "RECIPE\AIMAGE";
            white-space: pre-line;
            text-align: center;
            font-size: 8pt;
            font-weight: 600;
            color: #64748b;
            line-height: 1.2;
            display: flex;
            align-items: center;
            justify-content: center;
            height: 100%;
        }

        /* Main content centered layout */
        .main-content-centered {
            width: 100%;
            margin-top: 8pt;
        }

        /* Info sections grid layout for Storage, Preparation, Cooking, Allergens */
        .info-sections-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 6pt;
            margin-bottom: 8pt;
        }

        /* HACCP bottom section styling */
        .haccp-bottom-section {
            background: #fef3c7;
            border: 0.5pt solid #f59e0b;
            border-radius: 4pt;
            padding: 8pt;
            margin-top: 8pt;
            margin-bottom: 8pt;
            width: 100%;
        }

        .haccp-bottom-section .section-title {
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 8pt;
            font-size: 11pt;
            text-transform: uppercase;
            letter-spacing: 0.025em;
            border-bottom: 0.5pt solid #e2e8f0;
            padding-bottom: 4pt;
        }

        .haccp-content {
            font-size: 9pt;
            line-height: 1.4;
            color: #374151;
        }



        /* Enhanced professional info cards with modern styling */
        .recipe-info-cards {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 10pt;
            margin-bottom: 12pt;
            page-break-inside: avoid;
            break-inside: avoid;
            padding: 8pt;
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
            border-radius: 8pt;
            border: 1pt solid #e2e8f0;
            box-shadow: 0 1pt 3pt rgba(0, 0, 0, 0.1);
        }

        .info-card {
            background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
            border: 1pt solid #e2e8f0;
            border-radius: 8pt;
            padding: 12pt 10pt;
            text-align: center;
            box-shadow: 0 2pt 4pt rgba(0, 0, 0, 0.08);
            position: relative;
            overflow: hidden;
        }

        .info-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 2pt;
            background: linear-gradient(90deg, #3b82f6 0%, #2563eb 100%);
        }

        .info-card-label {
            font-size: 8pt;
            color: #64748b;
            margin-bottom: 4pt;
            text-transform: uppercase;
            font-weight: 500;
            letter-spacing: 0.025em;
        }

        .info-card-value {
            font-size: 11pt;
            font-weight: 600;
            color: #2563eb;
            line-height: 1.2;
        }

        /* Professional allergens section */
        .allergens-tags-section {
            background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
            border: 1pt solid #f59e0b;
            border-radius: 10pt;
            padding: 12pt;
            margin-bottom: 12pt;
            box-shadow: 0 3pt 6pt rgba(245, 158, 11, 0.2);
            page-break-inside: avoid;
            break-inside: avoid;
            position: relative;
            overflow: hidden;
        }

        .allergens-tags-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3pt;
            background: linear-gradient(90deg, #f59e0b 0%, #d97706 50%, #b45309 100%);
        }

        .allergens-tags-title {
            font-size: 10pt;
            font-weight: 700;
            color: #92400e;
            margin-bottom: 8pt;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            display: flex;
            align-items: center;
            gap: 6pt;
        }

        .allergens-tags-title::before {
            content: '!';
            color: #dc2626;
            font-size: 12pt;
            font-weight: bold;
            margin-right: 4pt;
            background: #fef2f2;
            border: 1pt solid #dc2626;
            border-radius: 50%;
            width: 12pt;
            height: 12pt;
            display: inline-flex;
            align-items: center;
            justify-content: center;
        }

        .allergens-text {
            font-size: 10pt;
            color: #374151;
            margin-bottom: 6pt;
            line-height: 1.4;
        }

        .tags-text {
            font-size: 9pt;
            color: #92400e;
            line-height: 1.4;
        }

        /* Pixel-perfect method section with precise alignment */
        .method-section {
            margin: 0 0 24pt 0;
            padding: 0;
            background: transparent;
            border: none;
            position: relative;
        }

        .method-section .section-title {
            color: #1f2937;
            font-weight: 700;
            font-size: 12pt;
            margin: 0 0 16pt 0;
            padding: 0 0 8pt 0;
            text-transform: uppercase;
            letter-spacing: 0.8pt;
            line-height: 1.2;
            display: block;
            text-align: left;
            border-bottom: 2pt solid #2563eb;
            position: relative;
        }

        .method-section .section-title::before {
            content: '▪';
            color: #2563eb;
            font-size: 12pt;
            margin-right: 6pt;
            transform: none;
            display: inline-block;
        }

        .method-step {
            margin: 0 0 20pt 0;
            padding: 0;
            background: transparent;
            border: none;
            font-size: 10pt;
            line-height: 1.6;
            page-break-inside: avoid;
            break-inside: avoid;
            position: relative;
            display: flex;
            align-items: flex-start;
            gap: 16pt;
        }

        .method-step::before {
            content: counter(step-counter);
            counter-increment: step-counter;
            width: 32pt;
            height: 32pt;
            background: #2563eb;
            color: #ffffff;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12pt;
            font-weight: 700;
            line-height: 1;
            flex-shrink: 0;
            margin-top: 2pt;
            box-shadow: 0 2pt 4pt rgba(37, 99, 235, 0.3);
        }

        .method-step:last-child {
            margin-bottom: 0;
        }

        .method-content {
            counter-reset: step-counter;
            margin: 0;
            padding: 0;
        }

        .method-step-content {
            flex: 1;
            min-width: 0;
        }

        /* Ensure method steps with images stay together */
        .method-step-with-image {
            page-break-inside: avoid;
            break-inside: avoid;
            display: flex;
            flex-direction: column;
        }

        .step-header {
            margin: 0 0 8pt 0;
            padding: 0;
            font-weight: 700;
            color: #1f2937;
            font-size: 11pt;
            line-height: 1.3;
            display: block;
        }

        .step-header strong {
            color: #2563eb;
            font-weight: 700;
            font-size: 11pt;
        }

        .step-content {
            display: block;
            margin: 0;
            padding: 0;
        }

        .step-description {
            color: #374151;
            font-size: 10pt;
            line-height: 1.6;
            font-weight: 400;
            margin: 0;
            padding: 0;
            text-align: left;
        }

        .step-description p {
            margin: 0 0 8pt 0;
            padding: 0;
        }

        .step-description p:last-child {
            margin-bottom: 0;
        }

        .step-description strong {
            color: #1f2937;
            font-weight: 600;
        }

        .step-description em {
            color: #6b7280;
            font-style: italic;
        }

        /* Step image container - restored for visibility */
        .step-image-container {
            width: 100pt;
            height: 75pt;
            margin: 12pt 0 0 0;
            display: block;
            border-radius: 6pt;
            background: #f9fafb;
            border: 1pt solid #e5e7eb;
            overflow: hidden;
            position: relative;
            box-shadow: 0 1pt 3pt rgba(0, 0, 0, 0.1);
        }

        .step-image {
            width: 100%;
            height: 100%;
            object-fit: cover;
            display: block;
            border-radius: 3pt;
        }



        /* Resource type indicators using simple text labels */
        .resource-type-indicator {
            display: inline-block;
            font-size: 8pt;
            font-weight: bold;
            color: #ffffff;
            background: #2563eb;
            padding: 2pt 4pt;
            border-radius: 2pt;
            margin-right: 6pt;
            font-family: 'Courier New', monospace;
            min-width: 28pt;
            text-align: center;
        }

        .resource-icon {
            display: inline-block;
            width: 12pt;
            height: 12pt;
            margin-right: 6pt;
            border-radius: 2pt;
            position: relative;
            vertical-align: middle;
        }

        .video-icon {
            background: #ef4444;
            border: 1pt solid #dc2626;
        }

        .video-icon::before {
            content: '▶';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: white;
            font-size: 6pt;
            font-weight: bold;
        }

        .pdf-icon {
            background: #f59e0b;
            border: 1pt solid #d97706;
        }

        .pdf-icon::before {
            content: 'PDF';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: white;
            font-size: 4pt;
            font-weight: bold;
            line-height: 1;
        }

        .doc-icon {
            background: #3b82f6;
            border: 1pt solid #2563eb;
        }

        .doc-icon::before {
            content: 'DOC';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: white;
            font-size: 4pt;
            font-weight: bold;
            line-height: 1;
        }

        .link-icon {
            background: #10b981;
            border: 1pt solid #059669;
            border-radius: 50%;
        }

        .link-icon::before {
            content: '↗';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: white;
            font-size: 6pt;
            font-weight: bold;
        }

        .resource-card {
            margin-bottom: 8px;
            padding: 6px;
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            font-size: 11px;
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        }

        .resource-header {
            display: flex;
            align-items: center;
            margin-bottom: 4px;
            font-weight: bold;
        }

        .resource-icon {
            margin-right: 6px;
            font-size: 13px;
        }

        .resource-title {
            color: #135e96;
            font-size: 12px;
        }

        .resource-description {
            margin-bottom: 6px;
            font-size: 11px;
            color: #666;
            font-style: italic;
        }

        .resource-image {
            width: 100%;
            max-width: 120pt;
            height: auto;
            max-height: 80pt;
            object-fit: cover;
            border-radius: 4pt;
            display: block;
            margin: 0 auto;
            border: 1pt solid #e5e7eb;
            background: #f9fafb;
        }

        .image-fallback {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            width: 120pt;
            height: 80pt;
            background: #f9fafb;
            border: 1pt dashed #d1d5db;
            border-radius: 4pt;
            color: #6b7280;
            font-size: 9pt;
            text-align: center;
            margin: 0 auto;
            gap: 4pt;
        }

        .image-fallback span:first-child {
            font-size: 16pt;
            opacity: 0.7;
        }

        .image-fallback span:last-child {
            font-size: 8pt;
            font-weight: 500;
        }

        .resource-link {
            text-align: center;
            margin-top: 4px;
        }

        .resource-link a {
            color: #135e96 !important;
            text-decoration: none !important;
            font-weight: 600 !important;
            background: #e3f2fd;
            padding: 4px 8px;
            border-radius: 4px;
            border: 1px solid #135e96;
            display: inline-block;
            font-size: 10px;
        }

        .image-fallback {
            text-align: center;
            color: #666;
            font-style: italic;
            padding: 12px;
            background: #f5f5f5;
            border-radius: 4px;
            border: 1px dashed #ccc;
            font-size: 11px;
        }





        /* Hidden class */
        .hidden {
            display: none !important;
        }

        /* HACCP items */
        .haccp-item {
            margin-bottom: 10px;
            padding: 8px;
            background: #fff3cd;
            border-left: 4px solid #ffc107;
            font-size: 12px;
            line-height: 1.4;
        }

        /* Menu section */
        .menu-section {
            margin-bottom: 15px;
        }

        .menu-title {
            font-weight: bold;
            color: #135e96;
            margin-bottom: 10px;
            font-size: 18px;
        }

        .menu-list {
            font-size: 14px;
            line-height: 1.4;
        }

        /* Professional costs and yield sections - Enhanced backgrounds */
        .costs-yield-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 8pt;
            margin-bottom: 8pt;
        }

        .cost-section {
            background: #f0fdf4;
            border: 0.5pt solid #22c55e;
            border-radius: 4pt;
            padding: 6pt;
        }

        .yield-section {
            background: #fef3c7;
            border: 0.5pt solid #f59e0b;
            border-radius: 4pt;
            padding: 6pt;
        }

        .section-title {
            font-weight: 700;
            color: #1e293b;
            margin-bottom: 10pt;
            font-size: 12pt;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            border-bottom: 2pt solid transparent;
            background: linear-gradient(90deg, #2563eb 0%, #3b82f6 100%) bottom/100% 2pt no-repeat;
            padding-bottom: 6pt;
            position: relative;
            display: flex;
            align-items: center;
            gap: 8pt;
        }

        .section-title::before {
            content: '◆';
            color: #2563eb;
            font-size: 10pt;
            margin-right: 6pt;
            transform: none;
        }

        .cost-item,
        .yield-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 4pt;
            font-size: 9pt;
            line-height: 1.4;
        }

        .cost-item:last-child,
        .yield-item:last-child {
            margin-bottom: 0;
        }

        /* Professional ingredients table */
        .ingredients-section {
            margin-bottom: 8pt;
        }

        .ingredients-table {
            width: 100%;
            border-collapse: separate;
            border-spacing: 0;
            font-size: 9pt;
            border: 3pt solid #1e40af;
            border-radius: 12pt;
            overflow: hidden;
            table-layout: fixed;
            /* Force table to respect column widths */
            box-shadow: 0 8pt 16pt rgba(30, 64, 175, 0.15), 0 2pt 4pt rgba(0, 0, 0, 0.1);
            background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
        }

        .ingredients-table th {
            background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%);
            color: white;
            padding: 12pt 8pt;
            text-align: center;
            font-weight: 700;
            font-size: 9pt;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            border-right: 2pt solid #1d4ed8;
            border-bottom: 2pt solid #1d4ed8;
            text-shadow: 0 1pt 3pt rgba(0, 0, 0, 0.4);
            position: relative;
        }

        .ingredients-table th::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 1pt;
            background: linear-gradient(90deg, transparent 0%, #60a5fa 50%, transparent 100%);
        }

        .ingredients-table th:first-child {
            text-align: center;
            border-top-left-radius: 9pt;
            border-left: none;
            /* Ingredient name header center-aligned */
        }

        .ingredients-table th:last-child {
            border-top-right-radius: 9pt;
            border-right: none;
        }

        .ingredients-table td {
            padding: 10pt 8pt;
            border-right: 1.5pt solid #cbd5e1;
            border-bottom: 1.5pt solid #cbd5e1;
            vertical-align: middle;
            line-height: 1.4;
            text-align: center;
            font-weight: 500;
            font-size: 9pt;
            color: #1e293b;
            /* Default center alignment for data */
            word-wrap: break-word;
            overflow-wrap: break-word;
            background: #ffffff;
            transition: background-color 0.2s ease;
        }

        /* Remove individual border overrides to maintain consistent table borders */

        .ingredients-table tbody tr:nth-child(even) {
            background: #f8fafc;
        }

        .ingredients-table tbody tr:nth-child(even) td {
            background: #f8fafc;
        }

        .ingredients-table tbody tr:hover {
            background: #e0f2fe;
        }

        .ingredients-table tbody tr:hover td {
            background: #e0f2fe;
        }

        .ingredients-table tbody tr:hover {
            background: #f1f5f9;
        }

        /* Add extra emphasis to outer borders */
        .ingredients-table tbody tr:first-child td {
            border-top: 3pt solid #000000;
        }

        .ingredients-table tbody tr:last-child td {
            border-bottom: 3pt solid #000000;
        }

        .ingredients-table td:first-child {
            border-left: 3pt solid #000000;
        }

        .ingredients-table td:last-child {
            border-right: 3pt solid #000000;
        }

        .ingredients-table th:first-child {
            border-left: 3pt solid #000000;
        }

        .ingredients-table th:last-child {
            border-right: 3pt solid #000000;
        }

        /* Prevent awkward table row breaks */
        .ingredients-table tbody tr {
            page-break-inside: avoid;
            break-inside: avoid;
        }

        /* Ensure table doesn't break mid-row */
        .ingredients-table {
            page-break-inside: auto;
        }

        .ingredients-table thead {
            display: table-header-group;
        }

        .ingredients-table tbody {
            display: table-row-group;
        }

        /* Ingredient column styling */
        .ingredient-name {
            font-weight: 600;
            color: #1e293b;
            text-align: center !important;
            width: 30%;
            /* Force center alignment for ingredient names */
        }

        .ingredient-quantity {
            font-weight: 500;
            color: #475569;
            text-align: center;
            width: 20%;
        }

        .ingredient-cost {
            font-family: 'Courier New', monospace;
            font-weight: 600;
            color: #dc2626;
            background: rgba(220, 38, 38, 0.1);
            border-radius: 3pt;
            text-align: center;
            width: 15%;
        }

        .ingredient-wastage {
            font-family: 'Courier New', monospace;
            font-weight: 600;
            color: #ea580c;
            background: rgba(234, 88, 12, 0.1);
            border-radius: 3pt;
            text-align: center;
            width: 15%;
        }

        .ingredient-final-cost {
            font-family: 'Courier New', monospace;
            font-weight: 700;
            color: #b91c1c;
            background: rgba(185, 28, 28, 0.15);
            border-radius: 3pt;
            border: 0.5pt solid rgba(185, 28, 28, 0.3);
            text-align: center;
            width: 20%;
        }



        /* Professional right column sections - Optimized spacing */
        .haccp-section {
            background: #fef3c7;
            border: 0.5pt solid #f59e0b;
            border-radius: 4pt;
            padding: 6pt;
            margin-bottom: 4pt;
        }

        .storage-section,
        .preparation-section,
        .cooking-section {
            background: #eff6ff;
            border: 0.5pt solid #3b82f6;
            border-radius: 4pt;
            padding: 6pt;
            margin-bottom: 4pt;
        }

        .allergens-section {
            background: #fef3c7;
            border: 0.5pt solid #f59e0b;
            border-radius: 4pt;
            padding: 6pt;
            margin-bottom: 4pt;
        }

        .section-header {
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 4pt;
            font-size: 9pt;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        .section-content {
            font-size: 8pt;
            line-height: 1.4;
            color: #374151;
        }

        .haccp-item {
            margin-bottom: 4px;
            padding: 4px;
            background: rgba(255, 255, 255, 0.7);
            border-radius: 3px;
            border-left: 2px solid #ffc107;
        }

        .haccp-item strong {
            color: #135e96;
            display: block;
            margin-bottom: 2px;
        }

        .allergen-item {
            display: flex;
            align-items: center;
            margin-bottom: 3px;
            font-size: 11px;
        }

        .allergen-icon {
            width: 16px;
            height: 16px;
            margin-right: 6px;
            border-radius: 3px;
        }

        /* Footer - Handled by Puppeteer, hide in HTML */
        .footer {
            display: none;
        }

        .footer-logo {
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .page-info {
            text-align: right;
        }

        /* Premium nutrition section with modern card design */
        .nutrition-section {
            margin-bottom: 16pt;
            padding: 16pt;
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
            border-radius: 12pt;
            border: 1pt solid #e2e8f0;
            box-shadow: 0 4pt 6pt rgba(0, 0, 0, 0.07);
            position: relative;
            overflow: hidden;
        }

        .nutrition-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3pt;
            background: linear-gradient(90deg, #10b981 0%, #059669 50%, #047857 100%);
        }

        .nutrition-section .section-title {
            color: #047857;
            font-weight: 700;
            font-size: 12pt;
            margin-bottom: 12pt;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            display: flex;
            align-items: center;
            gap: 8pt;
        }

        .nutrition-section .section-title::before {
            content: '▲';
            color: #2563eb;
            font-size: 12pt;
            margin-right: 6pt;
        }

        .nutrition-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 8pt;
            border: 0.5pt solid #e2e8f0;
            border-radius: 4pt;
            overflow: hidden;
        }

        .nutrition-table th {
            background: #1e293b;
            color: white;
            padding: 6pt 4pt;
            text-align: center;
            font-weight: 600;
            font-size: 8pt;
            text-transform: uppercase;
            letter-spacing: 0.025em;
        }

        .nutrition-table td {
            padding: 4pt;
            border-bottom: 0.5pt solid #e2e8f0;
            text-align: center;
            font-size: 8pt;
            line-height: 1.4;
        }

        .nutrition-table tbody tr:nth-child(even) {
            background: #f8fafc;
        }

        .tips-section {
            margin-bottom: 8pt;
            page-break-inside: avoid;
        }

        .tips-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 8pt;
        }

        .tip-card {
            background: #fefce8;
            border: 0.5pt solid #eab308;
            border-radius: 4pt;
            padding: 8pt;
            page-break-inside: avoid;
        }

        .tip-header {
            display: flex;
            align-items: center;
            margin-bottom: 4pt;
        }

        .tip-icon {
            margin-right: 4pt;
            font-size: 10pt;
        }

        .tip-title {
            font-weight: 600;
            color: #1e293b;
            font-size: 9pt;
        }

        .tip-content {
            font-size: 8pt;
            line-height: 1.5;
            color: #374151;
        }

        .serving-section {
            background: #f0fdf4;
            border: 0.5pt solid #16a34a;
            border-radius: 4pt;
            padding: 6pt;
            margin-bottom: 8pt;
            page-break-inside: avoid;
        }

        .serving-content {
            font-size: 9pt;
            line-height: 1.5;
            color: #374151;
        }

        /* Prevent premature page breaks */
        .method-section,
        .tips-section,
        .serving-section {
            page-break-inside: avoid;
            break-inside: avoid;
        }

        .ingredients-section {
            page-break-inside: auto;
            /* Allow ingredients table to break if needed */
        }

        /* Ensure content flows naturally */
        .main-content-centered {
            page-break-inside: auto;
        }

        .info-sections-grid {
            page-break-inside: avoid;
            break-inside: avoid;
        }

        .haccp-bottom-section {
            page-break-inside: avoid;
            break-inside: avoid;
        }

        /* Page layout - margins handled by Puppeteer */
        @page {
            size: A4;
        }

        /* Dynamic page breaks */
        .page-break-before {
            page-break-before: always;
        }

        .page-break-after {
            page-break-after: always;
        }

        /* Avoid breaking inside these elements */
        .avoid-break {
            page-break-inside: avoid;
            break-inside: avoid;
        }

        /* Print optimizations for dynamic content flow */
        @media print {
            body {
                margin: 0;
                padding: 0;
            }

            .container {
                padding: 0;
                margin: 0;
            }

            .main-content {
                padding: 0 5mm;
                padding-bottom: 0;
                /* Remove bottom padding as footer is handled by @page */
            }

            /* Keep sections together when possible */
            .recipe-header,
            .recipe-info-cards,
            .allergens-tags-section,
            .cost-section,
            .yield-section,
            .tips-section,
            .serving-section,
            .method-section,
            .info-sections-grid,
            .haccp-bottom-section {
                page-break-inside: avoid;
                break-inside: avoid;
            }

            /* Allow ingredients table to break naturally across pages */
            .ingredients-table {
                page-break-inside: auto;
            }

            /* Footer hidden - handled by Puppeteer */
            .footer {
                display: none;
            }
        }
    </style>
</head>

<body>
    <div class="container">
        <div class="main-content">
            <!-- Header with title and image -->
            <div class="recipe-header">
                <div class="recipe-title-section">
                    <h1 class="recipe-title">{{recipe_title}}</h1>
                    {{#if recipe_public_title}}
                    <div class="recipe-subtitle">{{recipe_public_title}}</div>
                    {{/if}}
                    {{#if recipe_description}}
                    <div class="recipe-description">{{recipe_description}}</div>
                    {{/if}}
                </div>
                {{#if recipe_image_url}}
                <div class="recipe-image-container">
                    <img src="{{recipe_image_url}}" alt="{{recipe_title}}" class="recipe-image"
                        onerror="this.style.display='none'; this.parentElement.classList.add('no-image');"
                        onload="if(!this.src || this.src.includes('undefined') || this.src.includes('null') || this.src === '' || this.src === window.location.href) { this.style.display='none'; this.parentElement.classList.add('no-image'); }">
                </div>
                {{else}}
                <div class="recipe-image-container no-image">
                    <div class="image-placeholder">No Image Available</div>
                </div>
                {{/if}}
            </div>

            <!-- Recipe Info Cards -->
            <div class="recipe-info-cards">
                <div class="info-card">
                    <div class="info-card-label">Prep Time</div>
                    <div class="info-card-value">{{formatTime recipe_preparation_time}}</div>
                </div>
                <div class="info-card">
                    <div class="info-card-label">Cook Time</div>
                    <div class="info-card-value">{{formatTime recipe_cook_time}}</div>
                </div>
                <div class="info-card">
                    <div class="info-card-label">Total Time</div>
                    <div class="info-card-value">{{formatTime (add recipe_preparation_time recipe_cook_time)}}</div>
                </div>
                <div class="info-card">
                    <div class="info-card-label">Servings</div>
                    <div class="info-card-value">{{#if recipe_yield}}{{recipe_yield}}{{else}}{{#if
                        recipe_total_portions}}{{recipe_total_portions}}{{else}}-{{/if}}{{/if}}</div>
                </div>
            </div>

            <!-- Allergens and Tags -->
            <div class="allergens-tags-section">
                <div class="allergens-tags-title">Allergens & Dietary Information</div>
                <div class="allergens-text">{{allergen_text}}</div>
                <div class="tags-text">{{recipe_tags}}</div>
            </div>

            <!-- Main Content - Centered Layout -->
            <div class="main-content-centered">
                <!-- Costs and Yield -->
                <div class="costs-yield-grid">
                    <div class="cost-section">
                        <div class="section-title">Costs</div>
                        <div class="cost-item">
                            <span>Batch cost</span>
                            <span>{{batch_cost}}</span>
                        </div>
                        <div class="cost-item">
                            <span>Serving cost</span>
                            <span>{{serving_cost}}</span>
                        </div>
                    </div>
                    <div class="yield-section">
                        <div class="section-title">Yield</div>
                        <div class="yield-item">
                            <span>Cooked weight</span>
                            <span>{{cooked_weight}}</span>
                        </div>
                        <div class="yield-item">
                            <span>Serving size</span>
                            <span>{{serving_size}}</span>
                        </div>
                        <div class="yield-item">
                            <span>Servings per batch</span>
                            <span>{{servings_per_batch}}</span>
                        </div>
                    </div>
                </div>

                <!-- Ingredients Table -->
                <div class="ingredients-section">
                    <div class="section-title">Ingredients</div>
                    <table class="ingredients-table">
                        <thead>
                            <tr>
                                <th style="width: 30%;">Ingredient</th>
                                <th style="width: 20%;">Qty</th>
                                <th style="width: 15%;">Cost</th>
                                <th style="width: 15%;">Wastage %</th>
                                <th style="width: 20%;">Final Cost</th>
                            </tr>
                        </thead>
                        <tbody>
                            {{#each ingredients}}
                            <tr>
                                <td class="ingredient-name"><strong>{{ingredient_name}}</strong></td>
                                <td class="ingredient-quantity">{{ingredient_quantity}} {{measure_title}}</td>
                                <td class="ingredient-cost">{{formattedCost}}</td>
                                <td class="ingredient-wastage">{{formattedWastage}}</td>
                                <td class="ingredient-final-cost">{{formattedFinalCost}}</td>
                            </tr>
                            {{/each}}
                        </tbody>
                    </table>
                </div>

                <!-- Instructions Section -->
                <div class="method-section">
                    <div class="section-title">Instructions</div>
                    <div class="method-content">
                        {{#each steps}}
                        <div class="method-step">
                            <div class="method-step-content">
                                <div class="step-header">
                                    <strong>Step {{recipe_step_order}}:</strong>
                                </div>
                                <div class="step-content">
                                    <div class="step-description">{{{recipe_step_description}}}</div>
                                    {{#if item_detail.item_link}}
                                    <div class="step-image-container">
                                        <img src="{{item_detail.item_link}}" alt="Step {{recipe_step_order}}"
                                            class="step-image">
                                    </div>
                                    {{/if}}
                                </div>
                            </div>
                        </div>
                        {{/each}}
                    </div>
                </div>



                <!-- Storage, Preparation, Cooking, and Allergens in a grid layout -->
                <div class="info-sections-grid">
                    <!-- Storage Section -->
                    <div class="storage-section avoid-break">
                        <div class="section-header">Storage</div>
                        <div class="section-content">{{storage_content}}</div>
                    </div>

                    <!-- Preparation Section -->
                    <div class="preparation-section avoid-break">
                        <div class="section-header">Preparation</div>
                        <div class="section-content">{{preparation_content}}</div>
                    </div>

                    <!-- Cooking Section -->
                    <div class="cooking-section avoid-break">
                        <div class="section-header">Cooking</div>
                        <div class="section-content">{{cooking_content}}</div>
                    </div>

                    <!-- Allergens Section -->
                    {{#if allergens_content}}
                    <div class="allergens-section avoid-break">
                        <div class="section-header">Allergens</div>
                        <div class="section-content">{{{allergens_content}}}</div>
                    </div>
                    {{/if}}
                </div>

                <!-- Chef Tips and FOH Notes -->
                {{#if (or recipe_head_chef_tips recipe_foh_tips)}}
                <div class="tips-section">
                    <div class="section-title">Chef Tips & FOH Notes</div>
                    <div class="tips-grid">
                        {{#if recipe_head_chef_tips}}
                        <div class="tip-card">
                            <div class="tip-header">
                                <span class="tip-title">Head Chef Notes</span>
                            </div>
                            <div class="tip-content">{{recipe_head_chef_tips}}</div>
                        </div>
                        {{/if}}
                        {{#if recipe_foh_tips}}
                        <div class="tip-card">
                            <div class="tip-header">
                                <span class="tip-title">FOH Notes</span>
                            </div>
                            <div class="tip-content">{{recipe_foh_tips}}</div>
                        </div>
                        {{/if}}
                    </div>
                </div>
                {{/if}}

                <!-- Serving Instructions -->
                {{#if serving_instructions}}
                <div class="serving-section avoid-break">
                    <div class="section-title">Serving Instructions</div>
                    <div class="serving-content">{{serving_instructions}}</div>
                </div>
                {{/if}}
            </div>

            <!-- HACCP Section - Moved to bottom -->
            {{#if haccp_attributes}}
            <div class="haccp-bottom-section avoid-break">
                <div class="section-title">HACCP</div>
                <div class="haccp-content">
                    {{#each haccp_attributes}}
                    <div class="haccp-item">
                        <strong>{{attribute_title}}</strong>
                        {{#if attribute_description}}{{attribute_description}}{{/if}}
                    </div>
                    {{/each}}
                </div>
            </div>
            {{/if}}

            <!-- Nutrition Section - Dynamic content that flows naturally -->
            {{#if nutrition_attributes}}
            <div class="nutrition-section page-break-before">
                <div class="section-title">Detailed Nutrition Information</div>
                <div class="nutrition-grid">
                    <div class="nutrition-column">
                        <table class="nutrition-table">
                            <thead>
                                <tr>
                                    <th>Nutrient</th>
                                    <th>Per 100g</th>
                                    <th>Per Serving</th>
                                </tr>
                            </thead>
                            <tbody>
                                {{#each nutrition_attributes}}
                                <tr>
                                    <td style="text-align: left; font-weight: bold;">{{attribute_title}}</td>
                                    <td>{{#if unit}}{{unit}}{{#if unit_of_measure}}
                                        {{unit_of_measure}}{{/if}}{{else}}-{{/if}}</td>
                                    <td>{{getPerServingValue this ../recipe_total_portions ../recipe_yield}}</td>
                                </tr>
                                {{/each}}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            {{/if}}
        </div>

        <!-- Footer - Will appear on every page automatically -->
        <div class="footer">
            <div class="footer-logo">
                <span>{{organization_name}}</span>
            </div>
            <div class="page-info">
                <div>Printed on {{generation_date}}</div>
                <div>Printed by: {{creator_user_full_name}}</div>
                <div>Page <span class="page-number"></span> of <span class="total-pages"></span></div>
            </div>
        </div>

    </div>

    <script>
        // Handle image loading issues
        document.addEventListener('DOMContentLoaded', function () {
            // Handle all images in the document
            const allImages = document.querySelectorAll('img');
            allImages.forEach(function (img) {
                // Check if image source is valid
                if (!img.src ||
                    img.src.includes('undefined') ||
                    img.src.includes('null') ||
                    img.src === '' ||
                    img.src === window.location.href) {
                    img.style.display = 'none';
                    if (img.parentElement) {
                        img.parentElement.classList.add('no-image');
                    }
                }

                // Handle load errors
                img.onerror = function () {
                    this.style.display = 'none';
                    if (this.parentElement) {
                        this.parentElement.classList.add('no-image');
                    }
                };

                // Handle successful loads but check for placeholder images
                img.onload = function () {
                    if (this.naturalWidth === 0 || this.naturalHeight === 0) {
                        this.style.display = 'none';
                        if (this.parentElement) {
                            this.parentElement.classList.add('no-image');
                        }
                    }
                };
            });

            // Handle resource images specifically
            const resourceImages = document.querySelectorAll('.resource-image');
            resourceImages.forEach(function (img) {
                img.onerror = function () {
                    this.style.display = 'none';
                    const fallback = this.parentElement.querySelector('.image-fallback');
                    if (fallback) {
                        fallback.style.display = 'block';
                    }
                };
            });
        });
    </script>

</body>

</html>