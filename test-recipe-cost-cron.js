/**
 * Test script to verify the recipe cost cron job fix
 * This script will test the recipeCostFreshnessCronJob function to ensure
 * the alias error is resolved
 */

// Set up environment
process.env.NEXT_NODE_ENV = 'development';

// Import required modules
const dotenv = require('dotenv');
dotenv.config();

// Load configuration
const config = require('./shared/config/config.json');
const dbconfig = require('./shared/config/db.json');

const env = process.env.NEXT_NODE_ENV || 'development';
global.config = JSON.parse(JSON.stringify(config))[env];
global.db = JSON.parse(JSON.stringify(dbconfig))[env];

// Import the cron job function
const { recipeCostFreshnessCronJob } = require('./src/cron/recipeCostFreshnessCron');

/**
 * Test the recipe cost freshness cron job
 */
async function testRecipeCostCron() {
  console.log('🧪 Testing Recipe Cost Freshness Cron Job...');
  console.log('Environment:', env);
  console.log('Database:', global.db.database);
  
  try {
    // Run the cron job
    const result = await recipeCostFreshnessCronJob();
    
    console.log('✅ Cron job completed successfully!');
    console.log('📊 Results:');
    console.log(`   - Total recipes checked: ${result.totalRecipesChecked}`);
    console.log(`   - Recipes with outdated costs: ${result.recipesWithOutdatedCosts}`);
    console.log(`   - Recipes updated: ${result.recipesUpdated}`);
    console.log(`   - Recipe updates failed: ${result.recipesUpdatesFailed}`);
    console.log(`   - Organizations processed: ${result.organizationsProcessed.length}`);
    console.log(`   - Execution time: ${result.executionTime}ms`);
    
    if (result.errors.length > 0) {
      console.log('⚠️  Errors encountered:');
      result.errors.forEach((error, index) => {
        console.log(`   ${index + 1}. ${error}`);
      });
    } else {
      console.log('✅ No errors encountered!');
    }
    
    process.exit(0);
    
  } catch (error) {
    console.error('❌ Cron job failed with error:');
    console.error(error.message);
    console.error('Stack trace:');
    console.error(error.stack);
    
    process.exit(1);
  }
}

// Run the test
testRecipeCostCron();
