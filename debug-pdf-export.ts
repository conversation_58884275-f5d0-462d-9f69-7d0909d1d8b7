/**
 * Test script to trigger PDF export and see debug output
 */

import dotenv from 'dotenv';
dotenv.config();

const env = process.env.NEXT_NODE_ENV || 'development';

// Load configuration
import config from './shared/config/config.json';
import dbconfig from './shared/config/db.json';

(global as any).config = JSON.parse(JSON.stringify(config))[env];
(global as any).db = JSON.parse(JSON.stringify(dbconfig))[env];

/**
 * Test PDF export with debugging
 */
async function testPDFExportWithDebug() {
  console.log('🧪 Testing PDF Export with Debug Output...');
  console.log('Environment:', env);
  console.log('Database:', (global as any).db.database);
  
  try {
    // Import the helper functions
    const { getRecipeByIdRaw, exportRecipeToPDF } = await import('./src/helper/recipe.helper');
    
    // Test with a specific recipe ID (change this to a recipe that has resources)
    const recipeId = 1; // Change this to your recipe ID
    const organizationId = 'your-org-id'; // Change this to your organization ID
    
    console.log(`📖 Fetching recipe ${recipeId}...`);
    
    // Get recipe data
    const recipe = await getRecipeByIdRaw(recipeId, organizationId);
    
    if (!recipe) {
      console.log('❌ Recipe not found');
      console.log('💡 Try changing the recipeId and organizationId in the script');
      process.exit(1);
    }
    
    console.log('✅ Recipe found:', recipe.recipe_title);
    console.log('📋 Recipe resources count:', recipe.resources?.length || 0);
    
    if (recipe.resources && recipe.resources.length > 0) {
      console.log('\n🔍 Raw resource data from database:');
      recipe.resources.forEach((resource: any, index: number) => {
        console.log(`Resource ${index + 1}:`, {
          id: resource.id,
          type: resource.type,
          item_link: resource.item_link,
          item_link_type: resource.item_link_type,
          item_detail: resource.item_detail
        });
      });
    }
    
    console.log('\n📄 Generating PDF with debug output...');
    const pdfBuffer = await exportRecipeToPDF(recipe);
    console.log('✅ PDF generated successfully, size:', pdfBuffer.length, 'bytes');
    
    // Save the PDF for inspection
    const fs = await import('fs');
    const filename = `debug-recipe-${recipeId}-${Date.now()}.pdf`;
    fs.writeFileSync(filename, pdfBuffer);
    console.log(`💾 PDF saved as: ${filename}`);
    
    process.exit(0);
    
  } catch (error: any) {
    console.error('❌ Test failed with error:');
    console.error(error.message);
    console.error('Stack trace:');
    console.error(error.stack);
    
    process.exit(1);
  }
}

// Run the test
testPDFExportWithDebug();
